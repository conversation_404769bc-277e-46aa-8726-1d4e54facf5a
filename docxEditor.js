/**
 * docxEditor.js - Utility for editing DOCX files
 * Provides functions to convert DOCX to editable text and back to DOCX
 */

/**
 * Extract plain text from a DOCX file for editing
 * @param {File} file - The DOCX file
 * @returns {Promise<string>} - Plain text content
 */
export async function extractTextFromDocx(file) {
    try {
        const arrayBuffer = await file.arrayBuffer();
        
        if (window.mammoth) {
            const result = await window.mammoth.extractRawText({ arrayBuffer });
            return result.value;
        } else {
            throw new Error('Mammoth.js library not loaded');
        }
    } catch (error) {
        console.error('Error extracting text from DOCX:', error);
        throw error;
    }
}

/**
 * Create a new DOCX file from plain text
 * @param {string} text - The plain text content
 * @param {string} filename - Original filename for reference
 * @returns {Promise<Blob>} - DOCX file as blob
 */
export async function createDocxFromText(text, filename) {
    try {
        if (!window.htmlDocx) {
            throw new Error('html-docx-js library not loaded');
        }

        // Convert plain text to HTML with proper paragraph formatting
        const htmlContent = text
            .split('\n')
            .map(line => line.trim() === '' ? '<p>&nbsp;</p>' : `<p>${escapeHtml(line)}</p>`)
            .join('');

        // Create a complete HTML document
        const fullHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Document</title>
            </head>
            <body>
                ${htmlContent}
            </body>
            </html>
        `;

        // Convert HTML to DOCX
        const docxBlob = window.htmlDocx.asBlob(fullHtml);
        return docxBlob;
    } catch (error) {
        console.error('Error creating DOCX from text:', error);
        throw error;
    }
}

/**
 * Escape HTML special characters
 * @param {string} text - Text to escape
 * @returns {string} - Escaped text
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Check if DOCX editing libraries are available
 * @returns {boolean} - True if libraries are loaded
 */
export function isDocxEditingAvailable() {
    return !!(window.mammoth && window.htmlDocx);
}

/**
 * Get a user-friendly error message for missing libraries
 * @returns {string} - Error message
 */
export function getDocxEditingErrorMessage() {
    if (!window.mammoth) {
        return 'Mammoth.js library not loaded. Cannot read DOCX files.';
    }
    if (!window.htmlDocx) {
        return 'html-docx-js library not loaded. Cannot create DOCX files.';
    }
    return 'DOCX editing libraries not properly initialized.';
}

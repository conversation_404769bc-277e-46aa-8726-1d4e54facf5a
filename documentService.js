
import * as dom from './domElements.js';
import * as state from './state.js';
import * as ui from './ui.js';
import * as docxEditor from './docxEditor.js';

// Alternative DOCX content extraction function
async function extractContentFromDocxZip(arrayBuffer) {
    try {
        // DOCX files are ZIP archives, try to extract document.xml
        const JSZip = window.JSZip;
        if (!JSZip) {
            console.warn('JSZip not available for alternative DOCX extraction');
            return null;
        }

        const zip = await JSZip.loadAsync(arrayBuffer);
        const documentXml = await zip.file('word/document.xml')?.async('text');

        if (documentXml) {
            console.log('Found document.xml, extracting text...');
            // Simple XML text extraction - remove XML tags and get text content
            const textContent = documentXml
                .replace(/<[^>]*>/g, ' ') // Remove XML tags
                .replace(/\s+/g, ' ') // Normalize whitespace
                .trim();

            console.log('Extracted text from document.xml:', textContent.substring(0, 200) + '...');
            return textContent;
        }

        return null;
    } catch (error) {
        console.error('Error in alternative DOCX extraction:', error);
        return null;
    }
}

export async function selectDocument(docId) {
    state.setCurrentDocId(docId);
    state.setCurrentDocumentFile(state.docFiles.find(df => df.id === docId));
    ui.renderDocList();
    await loadAndDisplayDocument();
    ui.updateAudioControlsUI(); // For TTS button status
}

export async function loadAndDisplayDocument() {
    if (!state.currentDocumentFile) {
        dom.docContentHeading.textContent = 'Content: None Selected';
        dom.docPlaceholder.style.display = 'block';
        dom.docEditorTextarea.style.display = 'none';
        dom.docHtmlViewer.style.display = 'none';
        dom.docEditorTextarea.value = '';
        dom.docHtmlViewer.innerHTML = '';
        state.setIsDocEditing(false);
        ui.updateEditModeUI();
        return;
    }

    dom.docContentHeading.textContent = `Content: ${state.currentDocumentFile.name}`;
    dom.docPlaceholder.style.display = 'none';
    const reader = new FileReader();

    if (state.currentDocumentFile.type === 'txt') {
        reader.onload = (e) => {
            const content = e.target.result;
            dom.docEditorTextarea.value = content;
            if (state.isDocEditing) {
                dom.docEditorTextarea.style.display = 'block';
                dom.docHtmlViewer.style.display = 'none';
            } else {
                dom.docHtmlViewer.innerHTML = `<pre>${content.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</pre>`;
                dom.docHtmlViewer.style.display = 'block';
                dom.docEditorTextarea.style.display = 'none';
            }
            // Reset edit mode on new doc load and ensure proper display
            state.setIsDocEditing(false);
            ui.updateEditModeUI();
        };
        reader.readAsText(state.currentDocumentFile.file);
    } else if (state.currentDocumentFile.type === 'docx') {
        // First validate the DOCX file
        const isValid = await docxEditor.validateDocxFile(state.currentDocumentFile.file);
        if (!isValid) {
            console.warn('DOCX file validation failed');
            dom.docHtmlViewer.innerHTML = '<p>This file does not appear to be a valid DOCX document. It may be corrupted or in an unsupported format.</p>';
            dom.docHtmlViewer.style.display = 'block';
            dom.docEditorTextarea.style.display = 'none';
            ui.updateStatus('Invalid DOCX file format');
            state.setIsDocEditing(false);
            ui.updateEditModeUI();
            return;
        }

        reader.onload = async (e) => {
            try {
                console.log(`Loading DOCX file: ${state.currentDocumentFile.name}`);
                console.log(`File size: ${e.target.result.byteLength} bytes`);
                console.log(`File type: ${state.currentDocumentFile.file.type}`);
                console.log(`File last modified: ${state.currentDocumentFile.file.lastModified}`);

                // Log first few bytes to check file signature
                const firstBytes = new Uint8Array(e.target.result.slice(0, 10));
                console.log(`First 10 bytes:`, Array.from(firstBytes).map(b => b.toString(16).padStart(2, '0')).join(' '));

                if (window.mammoth) {
                    let htmlResult = { value: '', messages: [] };
                    let textResult = { value: '' };

                    try {
                        // Convert to HTML for viewing
                        console.log('Converting DOCX to HTML...');
                        htmlResult = await window.mammoth.convertToHtml({ arrayBuffer: e.target.result });
                        console.log('HTML conversion successful, messages:', htmlResult.messages);

                        // Also extract plain text for editing
                        console.log('Extracting plain text...');
                        textResult = await window.mammoth.extractRawText({ arrayBuffer: e.target.result });
                        console.log('Text extraction successful, length:', textResult.value.length);
                        console.log('Extracted text content:', JSON.stringify(textResult.value));
                        console.log('HTML content:', JSON.stringify(htmlResult.value));
                    } catch (mammothError) {
                        console.error('=== MAMMOTH.JS EXTRACTION FAILED ===');
                        console.error('Error details:', mammothError);
                        console.error('Error name:', mammothError.name);
                        console.error('Error message:', mammothError.message);
                        console.error('Error stack:', mammothError.stack);

                        // Try alternative approach: read as ZIP and extract document.xml
                        try {
                            console.log('=== ATTEMPTING ZIP EXTRACTION ===');
                            const alternativeContent = await extractContentFromDocxZip(e.target.result);
                            if (alternativeContent && alternativeContent.trim().length > 0) {
                                textResult.value = alternativeContent;
                                htmlResult.value = `<p>${alternativeContent.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\n/g, '<br>')}</p>`;
                                console.log('=== ZIP EXTRACTION SUCCESSFUL ===');
                                console.log('Extracted content length:', alternativeContent.length);
                                console.log('Content preview:', alternativeContent.substring(0, 200));
                            } else {
                                console.log('=== ZIP EXTRACTION RETURNED EMPTY ===');
                            }
                        } catch (altError) {
                            console.error('=== ZIP EXTRACTION FAILED ===');
                            console.error('Error details:', altError);

                            // Last resort: try reading as plain text (in case it's a text file with .docx extension)
                            try {
                                console.log('=== ATTEMPTING PLAIN TEXT READING ===');
                                const textDecoder = new TextDecoder('utf-8');
                                const textContent = textDecoder.decode(e.target.result);
                                console.log('Plain text length:', textContent.length);
                                console.log('Plain text preview:', textContent.substring(0, 200));

                                if (textContent && textContent.trim().length > 0) {
                                    textResult.value = textContent;
                                    htmlResult.value = `<pre>${textContent.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>`;
                                    console.log('=== PLAIN TEXT READING SUCCESSFUL ===');
                                } else {
                                    console.log('=== PLAIN TEXT READING RETURNED EMPTY ===');
                                }
                            } catch (textError) {
                                console.error('=== PLAIN TEXT READING FAILED ===');
                                console.error('Error details:', textError);

                                // Fourth method: Try reading with different encodings
                                try {
                                    console.log('=== ATTEMPTING ALTERNATIVE ENCODINGS ===');
                                    const encodings = ['utf-8', 'utf-16', 'latin1', 'ascii'];
                                    for (const encoding of encodings) {
                                        try {
                                            const decoder = new TextDecoder(encoding);
                                            const content = decoder.decode(e.target.result);
                                            if (content && content.trim().length > 0 && !content.includes('\uFFFD')) {
                                                console.log(`=== SUCCESS WITH ${encoding.toUpperCase()} ENCODING ===`);
                                                console.log('Content length:', content.length);
                                                console.log('Content preview:', content.substring(0, 200));
                                                textResult.value = content;
                                                htmlResult.value = `<pre>${content.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>`;
                                                break;
                                            }
                                        } catch (encError) {
                                            console.log(`${encoding} encoding failed:`, encError.message);
                                        }
                                    }
                                } catch (finalError) {
                                    console.error('=== ALL EXTRACTION METHODS FAILED ===');
                                    console.error('Final error:', finalError);
                                }
                            }
                        }
                    }

                    // Store the plain text for editing
                    state.currentDocumentFile.plainTextContent = textResult.value;

                    if (state.isDocEditing) {
                        // Show in edit mode
                        dom.docEditorTextarea.value = textResult.value;
                        dom.docEditorTextarea.style.display = 'block';
                        dom.docHtmlViewer.style.display = 'none';
                        console.log('Displayed in edit mode');
                    } else {
                        // Show in view mode
                        dom.docHtmlViewer.innerHTML = htmlResult.value;
                        dom.docHtmlViewer.style.display = 'block';
                        dom.docEditorTextarea.style.display = 'none';
                        console.log('Displayed in view mode');
                    }

                    // Check if content is empty or contains only SSML markup
                    const hasVisibleContent = htmlResult.value.trim() || textResult.value.trim();
                    const isSSMLContent = textResult.value.trim().startsWith('<speak>') && textResult.value.trim().endsWith('</speak>');

                    console.log('Content analysis:');
                    console.log('- HTML content length:', htmlResult.value.length);
                    console.log('- Text content length:', textResult.value.length);
                    console.log('- HTML content trimmed:', htmlResult.value.trim().length);
                    console.log('- Text content trimmed:', textResult.value.trim().length);
                    console.log('- Has visible content:', hasVisibleContent);
                    console.log('- Is SSML content:', isSSMLContent);
                    console.log('- Text starts with <speak>:', textResult.value.trim().startsWith('<speak>'));
                    console.log('- Text ends with </speak>:', textResult.value.trim().endsWith('</speak>'));

                    if (!hasVisibleContent) {
                        console.warn('DOCX file appears to be empty - no content found');
                        dom.docHtmlViewer.innerHTML = '<p><em>This document appears to be empty.</em></p>';
                        dom.docHtmlViewer.style.display = 'block';
                        dom.docEditorTextarea.style.display = 'none';
                    } else if (isSSMLContent) {
                        console.log('DOCX contains SSML content');
                        // For SSML content, show it properly formatted
                        if (state.isDocEditing) {
                            // Already handled above
                        } else {
                            // Display SSML content with syntax highlighting in view mode
                            const ssmlFormatted = textResult.value
                                .replace(/</g, '&lt;')
                                .replace(/>/g, '&gt;')
                                .replace(/&lt;speak&gt;/g, '<span style="color: #0066cc; font-weight: bold;">&lt;speak&gt;</span>')
                                .replace(/&lt;\/speak&gt;/g, '<span style="color: #0066cc; font-weight: bold;">&lt;/speak&gt;</span>')
                                .replace(/&lt;break time="[^"]*"\/&gt;/g, '<span style="color: #cc6600; font-weight: bold;">$&</span>');

                            dom.docHtmlViewer.innerHTML = `<pre style="white-space: pre-wrap; font-family: monospace; background: #f5f5f5; padding: 10px; border-radius: 4px;">${ssmlFormatted}</pre>`;
                            dom.docHtmlViewer.style.display = 'block';
                            dom.docEditorTextarea.style.display = 'none';
                        }
                    } else {
                        // Fallback: show whatever content we have
                        console.log('Showing content as fallback');
                        if (htmlResult.value.trim()) {
                            // Use HTML content if available
                            dom.docHtmlViewer.innerHTML = htmlResult.value;
                        } else if (textResult.value.trim()) {
                            // Use plain text content as fallback
                            const escapedText = textResult.value.replace(/</g, '&lt;').replace(/>/g, '&gt;');
                            dom.docHtmlViewer.innerHTML = `<pre style="white-space: pre-wrap;">${escapedText}</pre>`;
                        }
                        dom.docHtmlViewer.style.display = 'block';
                        dom.docEditorTextarea.style.display = 'none';
                    }
                } else {
                    console.error('Mammoth.js library not loaded');
                    dom.docHtmlViewer.innerHTML = '<p>Mammoth.js library not loaded. Cannot display .docx.</p>';
                    dom.docHtmlViewer.style.display = 'block';
                    dom.docEditorTextarea.style.display = 'none';
                }
            } catch (err) {
                console.error('Error loading DOCX:', err);
                console.error('Error details:', {
                    name: err.name,
                    message: err.message,
                    stack: err.stack
                });

                // Provide more detailed error information
                let errorMessage = `Error loading DOCX: ${err.message}`;
                if (err.message.includes('not a valid zip file')) {
                    errorMessage += '<br><br><strong>Possible causes:</strong><ul><li>File may be corrupted</li><li>File may not be a valid DOCX format</li><li>File may have been created with incompatible software</li></ul>';
                }

                dom.docHtmlViewer.innerHTML = `<p>${errorMessage}</p>`;
                dom.docHtmlViewer.style.display = 'block';
                dom.docEditorTextarea.style.display = 'none';

                ui.updateStatus(`Failed to load DOCX file: ${err.message}`);
            }

            // Reset edit mode on new doc load and ensure proper display
            state.setIsDocEditing(false);
            ui.updateEditModeUI();
        };
        reader.onerror = (e) => {
            console.error('FileReader error:', e);
            dom.docHtmlViewer.innerHTML = '<p>Error reading file. The file may be corrupted or inaccessible.</p>';
            dom.docHtmlViewer.style.display = 'block';
            dom.docEditorTextarea.style.display = 'none';
            ui.updateStatus('Error reading file');
        };

        reader.readAsArrayBuffer(state.currentDocumentFile.file);
    }
}

export function toggleEditMode() {
    if (!state.currentDocumentFile) {
        ui.updateStatus('No document selected.');
        return;
    }

    const fileType = state.currentDocumentFile.type;
    if (fileType !== 'txt' && fileType !== 'docx') {
        ui.updateStatus('Editing is only supported for .txt and .docx files.');
        return;
    }

    // Check if DOCX editing is available for DOCX files
    if (fileType === 'docx' && !docxEditor.isDocxEditingAvailable()) {
        ui.updateStatus(docxEditor.getDocxEditingErrorMessage());
        return;
    }

    state.setIsDocEditing(!state.isDocEditing);
    ui.updateStatus(state.isDocEditing ? 'Edit mode enabled.' : 'View mode enabled.');
    ui.updateEditModeUI();
}

export function insertBreakTag(duration) {
    if (!state.isDocEditing || !state.currentDocumentFile || state.currentDocumentFile.type !== 'txt' || !dom.docEditorTextarea) {
        ui.updateStatus('Enable edit mode for a .txt file to insert break tags.');
        return;
    }
    const textarea = dom.docEditorTextarea;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const text = textarea.value;
    const breakTag = `<break time="${duration}s"/>`;
    const newText = text.substring(0, start) + breakTag + text.substring(end);
    textarea.value = newText;
    setTimeout(() => {
        textarea.focus();
        textarea.selectionStart = textarea.selectionEnd = start + breakTag.length;
    }, 0);
}

export async function saveDocument() {
    if (!state.currentDocumentFile || !state.isDocEditing) {
        ui.updateStatus('No document in edit mode to save.');
        return;
    }

    const fileType = state.currentDocumentFile.type;
    if (fileType !== 'txt' && fileType !== 'docx') {
        ui.updateStatus('Only .txt and .docx files can be saved.');
        return;
    }

    try {
        let blob;
        let filename = state.currentDocumentFile.name;

        if (fileType === 'txt') {
            // Save as plain text
            blob = new Blob([dom.docEditorTextarea.value], { type: 'text/plain;charset=utf-8' });
        } else if (fileType === 'docx') {
            // Check if DOCX editing is available
            if (!docxEditor.isDocxEditingAvailable()) {
                ui.updateStatus(docxEditor.getDocxEditingErrorMessage());
                return;
            }

            // Save as DOCX
            ui.updateStatus('Creating DOCX file...');
            blob = await docxEditor.createDocxFromText(dom.docEditorTextarea.value, filename);
        }

        // Try to use File System Access API if available (Chrome/Edge)
        if ('showSaveFilePicker' in window) {
            try {
                ui.updateStatus('Opening save dialog...');

                const fileHandle = await window.showSaveFilePicker({
                    suggestedName: filename,
                    types: [{
                        description: fileType === 'txt' ? 'Text files' : 'Word documents',
                        accept: fileType === 'txt' ?
                            { 'text/plain': ['.txt'] } :
                            { 'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'] }
                    }]
                });

                const writable = await fileHandle.createWritable();
                await writable.write(blob);
                await writable.close();

                ui.updateStatus(`Document saved successfully to ${filename}`);

                // Update the current document file reference to point to the saved file
                // Note: We can't get the actual File object back from the handle,
                // but we can update our internal state
                updateDocumentAfterSave(filename, blob, fileType);

                return;
            } catch (saveError) {
                if (saveError.name === 'AbortError') {
                    ui.updateStatus('Save cancelled by user.');
                    return;
                } else {
                    console.warn('File System Access API failed, falling back to download:', saveError);
                    ui.updateStatus('Direct save failed, downloading file instead...');
                }
            }
        }

        // Fallback: Download the file (original behavior)
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        ui.updateStatus(`Document ${filename} downloaded. Please manually replace the original file.`);

    } catch (error) {
        console.error('Error saving document:', error);
        ui.updateStatus(`Error saving document: ${error.message}`);
    }
}

// Helper function to update document state after successful save
function updateDocumentAfterSave(filename, blob, fileType) {
    try {
        // Create a new File object from the blob
        const newFile = new File([blob], filename, {
            type: fileType === 'txt' ? 'text/plain' : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        });

        // Update the current document file
        state.currentDocumentFile.file = newFile;

        // If it's a DOCX file, update the stored plain text content
        if (fileType === 'docx') {
            state.currentDocumentFile.plainTextContent = dom.docEditorTextarea.value;
        }

        console.log('Document state updated after save');
    } catch (error) {
        console.warn('Could not update document state after save:', error);
    }
}

// Function to save to original folder (requires user permission)
export async function saveToOriginalFolder() {
    if (!state.currentDocumentFile || !state.isDocEditing) {
        ui.updateStatus('No document in edit mode to save.');
        return;
    }

    // Check if File System Access API is available
    if (!('showDirectoryPicker' in window)) {
        ui.updateStatus('Direct folder access not supported in this browser. Use regular save instead.');
        return;
    }

    try {
        ui.updateStatus('Please select the folder where you want to save the document...');

        // Ask user to select the target folder
        const directoryHandle = await window.showDirectoryPicker();

        const fileType = state.currentDocumentFile.type;
        const filename = state.currentDocumentFile.name;

        let blob;
        if (fileType === 'txt') {
            blob = new Blob([dom.docEditorTextarea.value], { type: 'text/plain;charset=utf-8' });
        } else if (fileType === 'docx') {
            if (!docxEditor.isDocxEditingAvailable()) {
                ui.updateStatus(docxEditor.getDocxEditingErrorMessage());
                return;
            }
            ui.updateStatus('Creating DOCX file...');
            blob = await docxEditor.createDocxFromText(dom.docEditorTextarea.value, filename);
        }

        // Create or overwrite the file in the selected directory
        const fileHandle = await directoryHandle.getFileHandle(filename, { create: true });
        const writable = await fileHandle.createWritable();
        await writable.write(blob);
        await writable.close();

        ui.updateStatus(`Document saved successfully to folder: ${filename}`);
        updateDocumentAfterSave(filename, blob, fileType);

    } catch (error) {
        if (error.name === 'AbortError') {
            ui.updateStatus('Folder selection cancelled by user.');
        } else {
            console.error('Error saving to folder:', error);
            ui.updateStatus(`Error saving to folder: ${error.message}`);
        }
    }
}

export async function findAndSetCorrespondingDocument(trackId) {
    const audioFile = state.musicFiles.find(mf => mf.id === trackId);
    if (!audioFile) {
        state.setCurrentDocId(null);
        state.setCurrentDocumentFile(null);
        await loadAndDisplayDocument();
        return;
    }
    // Remove potential TTS prefixes like (G-TTS) or (MS-TTS) and the extension
    const audioBaseName = audioFile.name
        .replace(/^\((G-TTS|MS-TTS)\)\s*/i, '') // Remove (G-TTS) or (MS-TTS) prefix
        .substring(0, audioFile.name.replace(/^\((G-TTS|MS-TTS)\)\s*/i, '').lastIndexOf('.'))
        .toLowerCase();

    const foundDoc = state.docFiles.find(df =>
        df.name.substring(0, df.name.lastIndexOf('.')).toLowerCase() === audioBaseName
    );

    if (foundDoc) {
        if (state.currentDocId !== foundDoc.id) { // Only select if it's a different document
            await selectDocument(foundDoc.id);
        }
    } else {
        // If no corresponding document is found, clear the current document display
        // only if there was a document previously selected or if we want to ensure no stale doc is shown.
        if (state.currentDocId !== null) {
            state.setCurrentDocId(null);
            state.setCurrentDocumentFile(null);
            await loadAndDisplayDocument();
            ui.updateStatus(`No corresponding document found for ${audioFile.name}.`);
        }
    }
}

// ui.js - Updated: 2025-06-07 13:19

import * as dom from './domElements.js';
import * as state from './state.js';
import { G_TTS_API_KEY, MS_TTS_API_KEY, MS_TTS_SERVICE_REGION, BREAK_DURATIONS } from './constants.js';
import { formatTime, ssmlEscapeXmlEntities } from './utils.js';
import { ssmlSimpleHighlight } from './ssmlEditor.js';


// Callback types for more complex interactions to be passed from index.js
let selectMusicTrackCallback = () => {};
let selectDocumentCallback = () => {};
let insertBreakTagCallback = () => {};
let playReprocessedAudioCallback = (objectURL) => {};
let replaceOriginalFileCallback = (originalAudioFileId) => {};


export function registerUICallbacks(callbacks) {
    if (callbacks.selectMusicTrack) selectMusicTrackCallback = callbacks.selectMusicTrack;
    if (callbacks.selectDocument) selectDocumentCallback = callbacks.selectDocument;
    if (callbacks.insertBreakTag) insertBreakTagCallback = callbacks.insertBreakTag;
    if (callbacks.playReprocessedAudio) playReprocessedAudioCallback = callbacks.playReprocessedAudio;
    if (callbacks.replaceOriginalFile) replaceOriginalFileCallback = callbacks.replaceOriginalFile;
}

export function updateStatus(message) {
    if (dom.statusBar) {
        dom.statusBar.textContent = message;
    }
    console.log("Status:", message);
}

export function updatePlayPauseButton() {
    if (!dom.playPauseBtn) return;
    const icon = dom.playPauseBtn.querySelector('.material-icons');
    if (state.isPlaying && !state.isPausedManually) {
        icon.textContent = 'pause';
        dom.playPauseBtn.setAttribute('aria-label', 'Pause');
        dom.playPauseBtn.childNodes[1].nodeValue = " Pause";
    } else {
        icon.textContent = 'play_arrow';
        dom.playPauseBtn.setAttribute('aria-label', 'Play');
        dom.playPauseBtn.childNodes[1].nodeValue = " Play";
    }
}

export function getGTTSDisabledReason() {
    if (!G_TTS_API_KEY) return "Google TTS API Key not configured.";
    if (!state.currentDocumentFile && !state.isVerifying) return "Select a document.";
    if (state.currentDocumentFile && state.currentDocumentFile.type !== 'txt' && !state.isVerifying) return "Google TTS currently supports .txt file content for reprocessing.";
    if (state.areGVoicesLoading) return "Google Voices are loading...";
    return "";
}

export function getMsTTSDisabledReason() {
    if (!MS_TTS_API_KEY) return "Microsoft TTS API Key not configured.";
    if (!MS_TTS_SERVICE_REGION) return "Microsoft TTS Service Region not configured.";
    if (!state.currentDocumentFile && !state.isVerifying) return "Select a document.";
    if (state.currentDocumentFile && state.currentDocumentFile.type !== 'txt' && !state.isVerifying) return "Microsoft TTS currently supports .txt file content for reprocessing.";
    if (state.areMsVoicesLoading) return "Microsoft Voices are loading...";
    return "";
}

export function updateAudioControlsUI() {
    if (!dom.prevTrackBtn) return;

    const hasMusic = state.musicFiles.length > 0;
    const trackSelected = !!state.currentTrackId;
    dom.prevTrackBtn.disabled = !hasMusic || state.isVerifying;
    dom.playPauseBtn.disabled = !hasMusic || state.isVerifying;
    dom.nextTrackBtn.disabled = !hasMusic || state.isVerifying;
    dom.progressBar.disabled = !trackSelected || state.isVerifying;

    updatePlayPauseButton();

    const gTtsDisabledReason = getGTTSDisabledReason();
    dom.reprocessGTTSBtn.disabled = state.isGTTSreprocessing || !!gTtsDisabledReason || state.isVerifying;
    dom.reprocessGTTSBtn.title = gTtsDisabledReason || "Reprocess audio with Google Text-to-Speech";
    dom.gTtsVoiceSelect.disabled = state.isGTTSreprocessing || state.areGVoicesLoading || !G_TTS_API_KEY || state.isVerifying;

    const msTtsDisabledReason = getMsTTSDisabledReason();
    dom.reprocessMsTTSBtn.disabled = state.isMsTTSReprocessing || !!msTtsDisabledReason || state.isVerifying;
    dom.reprocessMsTTSBtn.title = msTtsDisabledReason || "Reprocess audio with Microsoft Text-to-Speech";
    dom.msTtsVoiceSelect.disabled = state.isMsTTSReprocessing || state.areMsVoicesLoading || !MS_TTS_API_KEY || !MS_TTS_SERVICE_REGION || state.isVerifying;

    if (dom.startVerificationBtn) {
        dom.startVerificationBtn.disabled = state.isVerifying || state.musicFiles.length === 0;
    }
}

export function renderMusicPlaylist() {
    if (!dom.musicPlaylistEl || !dom.noMusicMessage) return;
    dom.musicPlaylistEl.innerHTML = '';
    if (state.musicFiles.length === 0) {
        dom.noMusicMessage.style.display = 'block';
        dom.musicPlaylistEl.appendChild(dom.noMusicMessage);
        return;
    }
    dom.noMusicMessage.style.display = 'none';

    state.musicFiles.forEach(track => {
        const li = document.createElement('li');
        li.textContent = `${track.name} (${formatTime(track.duration || 0)})`;
        if (track.isSynthesized) {
            const icon = document.createElement('span');
            icon.className = 'material-icons';
            icon.style.fontSize = '1em';
            icon.style.marginRight = '5px';
            icon.style.verticalAlign = 'middle';
            icon.textContent = track.ttsProvider === 'microsoft' ? 'mic' : 'graphic_eq';
            if (track.ttsProvider === 'microsoft') icon.classList.add('ms-tts-icon');
            li.prepend(icon);
        }
        li.title = track.name;
        li.dataset.trackId = track.id;
        li.setAttribute('role', 'option');
        li.tabIndex = 0;
        if (track.id === state.currentTrackId) {
            li.classList.add('selected');
            li.setAttribute('aria-selected', 'true');
        }
        li.addEventListener('click', () => selectMusicTrackCallback(track.id));
        li.addEventListener('keydown', (e) => { if (e.key === 'Enter' || e.key === ' ') selectMusicTrackCallback(track.id); });
        dom.musicPlaylistEl.appendChild(li);
    });
}

export function renderDocList() {
    if (!dom.docListEl || !dom.noDocsMessage) return;
    dom.docListEl.innerHTML = '';
    if (state.docFiles.length === 0) {
        dom.noDocsMessage.style.display = 'block';
        dom.docListEl.appendChild(dom.noDocsMessage);
        return;
    }
    dom.noDocsMessage.style.display = 'none';

    state.docFiles.forEach(doc => {
        const li = document.createElement('li');
        li.textContent = doc.name;
        li.title = doc.name;
        li.dataset.docId = doc.id;
        li.setAttribute('role', 'option');
        li.tabIndex = 0;
        if (doc.id === state.currentDocId) {
            li.classList.add('selected');
            li.setAttribute('aria-selected', 'true');
        }
        li.addEventListener('click', () => selectDocumentCallback(doc.id));
        li.addEventListener('keydown', (e) => { if (e.key === 'Enter' || e.key === ' ') selectDocumentCallback(doc.id); });
        dom.docListEl.appendChild(li);
    });
}

export function renderBreakTagButtons() {
    if (!dom.breakTagButtonsEl) return;
    dom.breakTagButtonsEl.innerHTML = '';
    BREAK_DURATIONS.forEach(bd => {
        const button = document.createElement('button');
        button.textContent = bd.label;
        button.onclick = () => insertBreakTagCallback(bd.value);
        button.disabled = !state.isDocEditing || !state.currentDocumentFile ||
                         (state.currentDocumentFile.type !== 'txt' && state.currentDocumentFile.type !== 'docx');
        button.setAttribute('aria-label', `Insert ${bd.label}`);
        dom.breakTagButtonsEl.appendChild(button);
    });
}

export function updateEditModeUI() {
    if (!dom.toggleEditBtn || !dom.saveDocBtn || !dom.docEditorTextarea || !dom.docHtmlViewer) return;

    const canEdit = state.currentDocumentFile &&
                   (state.currentDocumentFile.type === 'txt' || state.currentDocumentFile.type === 'docx');
    const hasFileSystemAPI = 'showDirectoryPicker' in window;

    dom.toggleEditBtn.disabled = !canEdit;
    dom.saveDocBtn.disabled = !canEdit || !state.isDocEditing;

    // Enable "Save to Folder" button only if File System Access API is available and document is in edit mode
    if (dom.saveToFolderBtn) {
        dom.saveToFolderBtn.disabled = !canEdit || !state.isDocEditing || !hasFileSystemAPI;
        if (!hasFileSystemAPI) {
            dom.saveToFolderBtn.title = 'File System Access API not supported in this browser';
        } else {
            dom.saveToFolderBtn.title = 'Save document to a specific folder';
        }
    }

    const icon = dom.toggleEditBtn.querySelector('.material-icons');
    if (state.isDocEditing) {
        icon.textContent = 'visibility';
        dom.toggleEditBtn.childNodes[1].nodeValue = ' View Mode';
        dom.toggleEditBtn.setAttribute('aria-pressed', 'true');
        if (canEdit) {
            // For edit mode, always populate the textarea with the current content
            if (state.currentDocumentFile.type === 'txt') {
                // For txt files, get content from the pre element if it exists
                const preElement = dom.docHtmlViewer.querySelector('pre');
                if (preElement) {
                    dom.docEditorTextarea.value = preElement.textContent;
                }
            } else if (state.currentDocumentFile.type === 'docx') {
                // For docx files, use the stored plain text content
                if (state.currentDocumentFile.plainTextContent) {
                    dom.docEditorTextarea.value = state.currentDocumentFile.plainTextContent;
                }
            }
            dom.docEditorTextarea.style.display = 'block';
            dom.docHtmlViewer.style.display = 'none';
            dom.docEditorTextarea.focus();
        }
    } else {
        icon.textContent = 'edit';
        dom.toggleEditBtn.childNodes[1].nodeValue = ' Edit Mode';
        dom.toggleEditBtn.setAttribute('aria-pressed', 'false');
        if (canEdit) {
            if (state.currentDocumentFile.type === 'txt') {
                // For txt files, create a pre element with the current text content
                const currentTextContent = dom.docEditorTextarea.value;
                const preElement = document.createElement('pre');
                preElement.textContent = currentTextContent;
                dom.docHtmlViewer.innerHTML = '';
                dom.docHtmlViewer.appendChild(preElement);
                dom.docHtmlViewer.style.display = 'block';
                dom.docEditorTextarea.style.display = 'none';
            } else if (state.currentDocumentFile.type === 'docx') {
                // For docx files, the HTML viewer should already have the formatted content
                // Just ensure it's visible
                dom.docHtmlViewer.style.display = 'block';
                dom.docEditorTextarea.style.display = 'none';
            }
        } else if (state.currentDocumentFile?.type === 'docx') {
            // For DOCX files, show the HTML viewer in view mode
            dom.docHtmlViewer.style.display = 'block';
            dom.docEditorTextarea.style.display = 'none';
        } else {
            dom.docHtmlViewer.innerHTML = '';
            dom.docHtmlViewer.style.display = 'none';
            dom.docEditorTextarea.style.display = 'none';
        }
    }
    renderBreakTagButtons();
}

export function populateThemeSelector(themes, currentThemeName) {
    if (!dom.themeSelect) return;
    dom.themeSelect.innerHTML = '';
    Object.keys(themes).forEach(themeKey => {
        const option = document.createElement('option');
        option.value = themeKey;
        option.textContent = themes[themeKey].name;
        if (themeKey === currentThemeName) {
            option.selected = true;
        }
        dom.themeSelect.appendChild(option);
    });
}

export function updateSSMLStatusBar(message) {
    if (dom.ssmlStatusBar) dom.ssmlStatusBar.textContent = message;
    console.log("SSML Status:", message);
}

export function showSSMLModal(modalElement) {
    if (modalElement) modalElement.style.display = 'flex';
}

export function hideSSMLModal(modalElement) {
    if (modalElement) modalElement.style.display = 'none';
}

export function setActiveView(viewName) {
    console.log(`setActiveView called with: ${viewName}`);

    // Map view names to their corresponding DOM elements
    const viewMap = {
        'verification': {
            container: dom.appContainer,
            button: dom.audiobookVerificationBtn
        },
        'textEditor': {
            container: dom.textEditorViewContainer,
            button: dom.audiobookTextEditorBtn
        },
        'aiVoiceCreator': {
            container: dom.aiVoiceCreatorViewContainer,
            button: dom.aiVoiceCreatorBtn
        }
    };

    // Get all view containers and navigation buttons
    const allViews = [dom.appContainer, dom.textEditorViewContainer, dom.aiVoiceCreatorViewContainer];
    const allButtons = [dom.audiobookVerificationBtn, dom.audiobookTextEditorBtn, dom.aiVoiceCreatorBtn];

    console.log('Available views:', allViews.map(v => v ? v.id : 'null'));
    console.log('Available buttons:', allButtons.map(b => b ? b.id : 'null'));

    // Hide all views
    allViews.forEach(view => {
        if (view) {
            view.classList.add('hidden-view');
            console.log(`Hidden view: ${view.id}`);
        }
    });

    // Remove active state from all buttons
    allButtons.forEach(button => {
        if (button) {
            button.classList.remove('active-nav-button');
            console.log(`Deactivated button: ${button.id}`);
        }
    });

    // Show the requested view and activate its button
    const targetView = viewMap[viewName];
    if (targetView) {
        if (targetView.container) {
            targetView.container.classList.remove('hidden-view');
            console.log(`Showed view: ${targetView.container.id}`);
        }
        if (targetView.button) {
            targetView.button.classList.add('active-nav-button');
            console.log(`Activated button: ${targetView.button.id}`);
        }
    } else {
        console.warn(`Unknown view name: ${viewName}`);
    }
}

export function renderVerificationResults() {
    if (!dom.verificationResultsListEl || !dom.noVerificationResultsMessage) return;
    dom.verificationResultsListEl.innerHTML = '';

    if (state.verificationResults.length === 0 && !state.isVerifying) {
        dom.noVerificationResultsMessage.style.display = 'block';
        dom.verificationResultsListEl.appendChild(dom.noVerificationResultsMessage);
        updateVerificationOverallStatus("Verification has not started or no results yet.");
        return;
    }
    dom.noVerificationResultsMessage.style.display = 'none';

    const overallStatusText = state.isVerifying ?
        `Processing file ${state.currentVerificationFileIndex + 1} of ${state.musicFiles.length}...` :
        `Verification complete. ${state.verificationResults.length} files processed.`;
    updateVerificationOverallStatus(overallStatusText);

    state.verificationResults.forEach(result => {
        const li = document.createElement('li');
        li.dataset.originalAudioId = result.originalAudioFile.id;

        const header = document.createElement('div');
        header.className = 'verification-result-header';
        header.textContent = result.originalAudioFile.name;
        li.appendChild(header);

        const details = document.createElement('div');
        details.className = 'verification-result-details';

        let currentStatus = result.status;
        if (result.replaced) {
            currentStatus = "Replaced with Correction";
        }
        const statusSpan = document.createElement('span');
        statusSpan.textContent = `Status: ${currentStatus}`;
        statusSpan.className = `status-${currentStatus.toLowerCase().replace(/[\s&]+/g, '-')}`;
        details.appendChild(statusSpan);

        if (result.whisperTranscriptPreview) {
            const transcriptP = document.createElement('p');
            transcriptP.innerHTML = `<strong>Whisper (Simulated):</strong> "${ssmlEscapeXmlEntities(result.whisperTranscriptPreview)}..."`;
            details.appendChild(transcriptP);
        }
        if (result.aiAnalysis?.identifiedIssues > 0 && !result.replaced) {
            const issuesP = document.createElement('p');
            issuesP.innerHTML = `<strong>AI Analysis (Simulated):</strong> Identified ${result.aiAnalysis.identifiedIssues} potential issue(s).`;
            details.appendChild(issuesP);
        }
        li.appendChild(details);

        const actions = document.createElement('div');
        actions.className = 'verification-result-actions';

        if (result.aiAnalysis?.ssmlText && !result.replaced) {
            const viewSSMLBtn = document.createElement('button');
            viewSSMLBtn.className = 'control-button info-button';
            viewSSMLBtn.innerHTML = `<span class="material-icons">description</span> View SSML`;
            viewSSMLBtn.onclick = () => {
                dom.viewSSMLTextModalTitle.textContent = `SSML for: ${result.originalAudioFile.name}`;
                dom.viewSSMLModalContentArea.innerHTML = ssmlSimpleHighlight(result.aiAnalysis.ssmlText);
                showSSMLModal(dom.viewSSMLTextModal);
            };
            actions.appendChild(viewSSMLBtn);
        }

        if (result.reprocessedAudio?.objectURL && !result.replaced) {
            const playReprocessedBtn = document.createElement('button');
            playReprocessedBtn.className = 'control-button accent-button';
            playReprocessedBtn.innerHTML = `<span class="material-icons">play_circle_filled</span> Play Corrected`;
            playReprocessedBtn.onclick = () => playReprocessedAudioCallback(result.reprocessedAudio.objectURL);
            actions.appendChild(playReprocessedBtn);

            const replaceBtn = document.createElement('button');
            replaceBtn.className = 'control-button warning-button';
            replaceBtn.innerHTML = `<span class="material-icons">autorenew</span> Replace Original`;
            replaceBtn.disabled = state.isVerifying || !!result.replaced;
            replaceBtn.onclick = () => {
                if (confirm(`Are you sure you want to replace "${result.originalAudioFile.name}" and its corresponding document? This cannot be undone.`)) {
                    replaceOriginalFileCallback(result.originalAudioFile.id);
                }
            };
            actions.appendChild(replaceBtn);
        }

        if (actions.hasChildNodes()) li.appendChild(actions);
        dom.verificationResultsListEl.appendChild(li);
    });
}

export function updateVerificationOverallStatus(message) {
    if (dom.verificationOverallStatus) {
        dom.verificationOverallStatus.textContent = message;
    }
}

export function populateGoogleVoiceSelector(selectElement, voices, defaultVoiceName) {
    if (!selectElement) return;
    selectElement.innerHTML = '';

    if (state.areGVoicesLoading) {
        selectElement.innerHTML = '<option value="">Loading Google voices...</option>';
        selectElement.disabled = true;
        return;
    }
    if (!G_TTS_API_KEY) {
        selectElement.innerHTML = '<option value="">Google TTS N/A: No API Key</option>';
        selectElement.disabled = true;
        return;
    }
    if (voices.length === 0) {
        selectElement.innerHTML = '<option value="">No Google voices found</option>';
        selectElement.disabled = true;
    } else {
        voices.forEach(voice => {
            const option = document.createElement('option');
            option.value = voice.name;
            option.textContent = voice.label;
            selectElement.appendChild(option);
        });
        const defaultVoiceExists = voices.some(v => v.name === defaultVoiceName);
        selectElement.value = defaultVoiceExists ? defaultVoiceName : (voices[0]?.name || '');
        if (selectElement === dom.gTtsVoiceSelect) {
            state.setSelectedGTTSVoiceName(selectElement.value);
        }
        selectElement.disabled = false;
    }
    if (selectElement === dom.gTtsVoiceSelect) {
        selectElement.disabled = state.isGTTSreprocessing || state.areGVoicesLoading || !G_TTS_API_KEY || state.isVerifying;
    }
}

export function populateMicrosoftVoiceSelector(selectElement, voices, defaultVoiceName) {
    if (!selectElement) return;
    selectElement.innerHTML = '';

    if (state.areMsVoicesLoading) {
        selectElement.innerHTML = '<option value="">Loading MS voices...</option>';
        selectElement.disabled = true;
        return;
    }
    if (!MS_TTS_API_KEY || !MS_TTS_SERVICE_REGION) {
        selectElement.innerHTML = '<option value="">MS TTS N/A: Key/Region Missing</option>';
        selectElement.disabled = true;
        return;
    }
    if (voices.length === 0) {
        selectElement.innerHTML = '<option value="">No MS voices found</option>';
        selectElement.disabled = true;
    } else {
        voices.forEach(voice => {
            const option = document.createElement('option');
            option.value = voice.name;
            option.textContent = voice.label;
            selectElement.appendChild(option);
        });
        const defaultVoiceExists = voices.some(v => v.name === defaultVoiceName);
        selectElement.value = defaultVoiceExists ? defaultVoiceName : (voices[0]?.name || '');
        if (selectElement === dom.msTtsVoiceSelect) {
            state.setSelectedMsTTSVoiceName(selectElement.value);
        }
        selectElement.disabled = false;
    }
    if (selectElement === dom.msTtsVoiceSelect) {
        selectElement.disabled = state.isMsTTSReprocessing || state.areMsVoicesLoading || !MS_TTS_API_KEY || !MS_TTS_SERVICE_REGION || state.isVerifying;
    }
}


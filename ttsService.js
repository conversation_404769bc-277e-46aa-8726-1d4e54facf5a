/********************************************************************
 *  ttsService.js
 *  ---------------------------------------------------------------
 *  Google Cloud Text-to-Speech  +  Microsoft Azure TTS (live REST)
 *******************************************************************/

import * as dom   from './domElements.js';
import * as state from './state.js';
import * as ui    from './ui.js';

import {
  /* GOOGLE CLOUD TTS */
  G_TTS_API_KEY,
  DEFAULT_G_TTS_VOICE_NAME,
  EXTENSION_TO_ENCODING_GOOGLE,
  G_TTS_SPEECH_SPEED,
  G_TTS_SAMPLE_RATE_HERTZ,

  /* MICROSOFT */
  MS_TTS_API_KEY,
  DEFAULT_MS_TTS_VOICE_NAME,
  MS_TTS_SPEECH_SPEED,
  MS_TTS_SAMPLE_RATE_HERTZ,
  MS_TTS_SERVICE_REGION,
} from './constants.js';

import {
  base64ToBlob,
  naturalSort,
  ssmlEscapeXmlEntities,
} from './utils.js';

import { selectMusicTrack } from './audioService.js';

/*───────────────────────────────────────────────────────────────────
  Shared helper – update playlist & UI after synthesis
───────────────────────────────────────────────────────────────────*/
function updateMusicFileListWithNewTTS(entry, provider, verificationCtx = false) {
  if (verificationCtx) {
    console.log(`${provider} audio "${entry.name}" created for verification.`);
    return;
  }

  const base = entry.name.replace(/^\((G-TTS|MS-TTS)\)\s*/, '');
  const oldIdx = state.musicFiles.findIndex(
    f => f.isSynthesized && f.name.replace(/^\((G-TTS|MS-TTS)\)\s*/, '') === base,
  );

  if (oldIdx !== -1) {
    const old = state.musicFiles[oldIdx];
    if (old.objectURL && old.objectURL !== entry.objectURL) URL.revokeObjectURL(old.objectURL);
    state.musicFiles.splice(oldIdx, 1);
  }

  state.musicFiles.push(entry);
  state.musicFiles.sort((a, b) => naturalSort(a.name, b.name));

  ui.renderMusicPlaylist();
  ui.updateStatus(`${provider} audio "${entry.name}" created and added to playlist.`);
  selectMusicTrack(entry.id);
}

/*===================================================================
  GOOGLE CLOUD TTS
===================================================================*/

/** 1️⃣ Fetch Google Cloud TTS voice catalogue */
export async function fetchAvailableGoogleVoices() {
  console.log('fetchAvailableGoogleVoices: Starting...');
  console.log('fetchAvailableGoogleVoices: G_TTS_API_KEY available:', !!G_TTS_API_KEY);
  console.log('fetchAvailableGoogleVoices: G_TTS_API_KEY length:', G_TTS_API_KEY?.length || 0);

  if (!G_TTS_API_KEY) {
    console.log('fetchAvailableGoogleVoices: No API key, returning empty array');
    return [];
  }

  state.setAreGVoicesLoading(true);
  ui.updateStatus('Fetching Google Cloud TTS voices…');
  dom.gTtsVoiceSelect.innerHTML = '<option value="">Loading Google voices…</option>';
  ui.updateAudioControlsUI();

  try {
    // Use Google Cloud Text-to-Speech REST API
    const url = `https://texttospeech.googleapis.com/v1/voices?key=${G_TTS_API_KEY}`;
    console.log('fetchAvailableGoogleVoices: Making request to:', url.replace(G_TTS_API_KEY, 'HIDDEN_KEY'));

    const res = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      }
    });
    console.log('fetchAvailableGoogleVoices: Response status:', res.status);
    console.log('fetchAvailableGoogleVoices: Response ok:', res.ok);

    if (!res.ok) {
      const data = await res.json();
      console.log('fetchAvailableGoogleVoices: Error response:', data);
      throw new Error(data.error?.message || res.statusText);
    }

    const responseData = await res.json();
    console.log('fetchAvailableGoogleVoices: Response data:', responseData);

    const { voices: raw } = responseData;
    console.log('fetchAvailableGoogleVoices: Raw voices count:', raw?.length || 0);

    const voices = raw
      .map(v => ({
        name: v.name,
        label: `${v.name} (${v.languageCodes[0]}${v.ssmlGender ? ' - ' + v.ssmlGender : ''})`,
        languageCode: v.languageCodes[0],
        ssmlGender: v.ssmlGender,
      }))
      .sort((a, b) => a.label.localeCompare(b.label));

    console.log('fetchAvailableGoogleVoices: Processed voices count:', voices.length);
    state.setFetchedGTTSVoices(voices);
    dom.gTtsVoiceSelect.innerHTML = '';

    if (voices.length === 0) {
      console.log('fetchAvailableGoogleVoices: No voices found, showing error message');
      dom.gTtsVoiceSelect.innerHTML = '<option value="">No Google voices found</option>';
    } else {
      voices.forEach(v => {
        const opt = document.createElement('option');
        opt.value = v.name;
        opt.textContent = v.label;
        dom.gTtsVoiceSelect.appendChild(opt);
      });

      const def = voices.find(v => v.name === DEFAULT_G_TTS_VOICE_NAME) || voices[0];
      state.setSelectedGTTSVoiceName(def.name);
      dom.gTtsVoiceSelect.value = def.name;
    }

    ui.updateStatus('Google Cloud TTS voices loaded.');
    return voices;
  } catch (err) {
    console.error('Error fetching Google Cloud TTS voices:', err);
    console.error('Error type:', err.constructor.name);
    console.error('Error message:', err.message);
    console.error('Error stack:', err.stack);

    // Check if it's a CORS error
    if (err.message.includes('CORS') || err.message.includes('cross-origin') || err.name === 'TypeError') {
      console.error('This appears to be a CORS error - Google Cloud TTS API may not support direct browser requests');
      dom.gTtsVoiceSelect.innerHTML = '<option value="">CORS Error: API not accessible from browser</option>';
      ui.updateStatus('Error: Google Cloud TTS API blocked by CORS policy. Consider using a server-side proxy.');
    } else {
      dom.gTtsVoiceSelect.innerHTML = '<option value="">Error loading Google voices</option>';
      ui.updateStatus(`Error loading Google voices: ${err.message}`);
    }

    state.setFetchedGTTSVoices([]);
    return [];
  } finally {
    state.setAreGVoicesLoading(false);
    ui.updateAudioControlsUI();
  }
}

/** 2️⃣ Low-level Google Cloud TTS synth */
async function synthesizeWithGoogleTTS(
  ssml,
  voiceName,
  langCode,
  audioEncoding = 'MP3',
  sampleRate = G_TTS_SAMPLE_RATE_HERTZ,
) {
  if (!G_TTS_API_KEY) throw new Error('Google Cloud TTS API key missing.');

  const res = await fetch(
    `https://texttospeech.googleapis.com/v1/text:synthesize?key=${G_TTS_API_KEY}`,
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        input: { ssml },
        voice: { languageCode: langCode, name: voiceName },
        audioConfig: { audioEncoding, sampleRateHertz: sampleRate },
      }),
    },
  );

  if (!res.ok) {
    const data = await res.json();
    throw new Error(data.error?.message || res.statusText);
  }

  const { audioContent } = await res.json();
  const mime =
    audioEncoding === 'LINEAR16'
      ? 'audio/wav'
      : audioEncoding === 'OGG_OPUS'
      ? 'audio/ogg'
      : audioEncoding === 'FLAC'
      ? 'audio/flac'
      : 'audio/mpeg';

  return { audioBlob: base64ToBlob(audioContent, mime), mimeType: mime };
}

/** 3️⃣ Re-process current doc with Google TTS (UI button) */
export async function handleReprocessWithGoogleTTS() {
  const reason = ui.getGTTSDisabledReason();
  if (reason) {
    ui.updateStatus(`Cannot reprocess with Google TTS: ${reason}`);
    return;
  }

  const voice = state.fetchedGTTSVoices.find(v => v.name === state.selectedGTTSVoiceName);
  if (!voice) {
    ui.updateStatus('Selected Google TTS voice is invalid.');
    return;
  }

  state.setIsGTTSreprocessing(true);
  ui.updateStatus(`Reprocessing "${state.currentDocumentFile.name}" with Google TTS (${voice.label})…`);
  ui.updateAudioControlsUI();

  try {
    let text = '';
    if (state.isDocEditing) {
      text = dom.docEditorTextarea.value;
    } else {
      // Handle both txt (pre element) and docx (HTML content) files
      if (state.currentDocumentFile.type === 'txt') {
        text = dom.docHtmlViewer.querySelector('pre')?.textContent || '';
      } else if (state.currentDocumentFile.type === 'docx') {
        // For DOCX files, extract text from HTML content
        text = dom.docHtmlViewer.textContent || dom.docHtmlViewer.innerText || '';
      }
    }

    /* Build SSML */
    const ssml = /^<speak\b/i.test(text.trim())
      ? text
      : `<speak><prosody rate="${G_TTS_SPEECH_SPEED}">${ssmlEscapeXmlEntities(text)}</prosody></speak>`;

    const ext = state.currentAudioFile.name.slice(state.currentAudioFile.name.lastIndexOf('.')).toLowerCase();
    const enc = EXTENSION_TO_ENCODING_GOOGLE[ext] || 'MP3';

    const { audioBlob, mimeType } = await synthesizeWithGoogleTTS(
      ssml,
      voice.name,
      voice.languageCode,
      enc,
    );

    const base = state.currentAudioFile.name.replace(/^\((G-TTS|MS-TTS)\)\s*/, '').replace(ext, '');
    const fileName = `(G-TTS) ${base}${ext}`;
    const url = URL.createObjectURL(audioBlob);

    const entry = {
      id: `${fileName}_${Date.now()}`,
      file: new File([audioBlob], fileName, { type: mimeType }),
      name: fileName,
      duration: 0,
      objectURL: url,
      isSynthesized: true,
      ttsProvider: 'google',
    };

    const tmp = new Audio(url);
    tmp.onloadedmetadata = () => {
      entry.duration = tmp.duration;
      updateMusicFileListWithNewTTS(entry, 'Google TTS');
    };
    tmp.onerror = () => updateMusicFileListWithNewTTS(entry, 'Google TTS');
  } catch (err) {
    console.error('Google TTS error:', err);
    ui.updateStatus(`Error during Google TTS reprocessing: ${err.message}`);
  } finally {
    state.setIsGTTSreprocessing(false);
    ui.updateAudioControlsUI();
  }
}

/*===================================================================
  MICROSOFT AZURE – LIVE REST IMPLEMENTATION
===================================================================*/

/** 1️⃣ Fetch Azure voice catalogue */
export async function fetchAvailableMicrosoftVoices() {
  console.log('fetchAvailableMicrosoftVoices: Starting...');
  console.log('fetchAvailableMicrosoftVoices: MS_TTS_API_KEY available:', !!MS_TTS_API_KEY);
  console.log('fetchAvailableMicrosoftVoices: MS_TTS_API_KEY length:', MS_TTS_API_KEY?.length || 0);
  console.log('fetchAvailableMicrosoftVoices: MS_TTS_SERVICE_REGION available:', !!MS_TTS_SERVICE_REGION);
  console.log('fetchAvailableMicrosoftVoices: MS_TTS_SERVICE_REGION value:', MS_TTS_SERVICE_REGION);

  if (!MS_TTS_API_KEY || !MS_TTS_SERVICE_REGION) {
    console.log('fetchAvailableMicrosoftVoices: Missing API key or region, returning empty array');
    state.setAreMsVoicesLoading(false);
    dom.msTtsVoiceSelect.innerHTML =
      '<option value="">MS TTS N/A: Key or Region Missing</option>';
    dom.msTtsVoiceSelect.disabled = true;
    dom.reprocessMsTTSBtn.disabled = true;
    ui.updateAudioControlsUI();
    return [];
  }

  state.setAreMsVoicesLoading(true);
  ui.updateStatus('Fetching Microsoft TTS voices…');
  dom.msTtsVoiceSelect.innerHTML = '<option value="">Loading MS voices…</option>';
  ui.updateAudioControlsUI();

  try {
    const res = await fetch(
      `https://${MS_TTS_SERVICE_REGION}.tts.speech.microsoft.com/cognitiveservices/voices/list`,
      { headers: { 'Ocp-Apim-Subscription-Key': MS_TTS_API_KEY } },
    );
    if (!res.ok) {
      const txt = await res.text();
      throw new Error(`Azure voice list failed (${res.status}): ${txt || res.statusText}`);
    }

    const raw = await res.json();
    const voices = raw
      .map(v => ({
        name: v.ShortName,
        label: `${v.ShortName} (${v.Locale}${v.Gender ? ' - ' + v.Gender : ''})`,
        languageCode: v.Locale,
        ssmlGender: v.Gender,
      }))
      .sort((a, b) => a.label.localeCompare(b.label));

    state.setFetchedMsTTSVoices(voices);
    dom.msTtsVoiceSelect.innerHTML = '';

    if (voices.length === 0) {
      dom.msTtsVoiceSelect.innerHTML = '<option value="">No MS voices found</option>';
    } else {
      voices.forEach(v => {
        const opt = document.createElement('option');
        opt.value = v.name;
        opt.textContent = v.label;
        dom.msTtsVoiceSelect.appendChild(opt);
      });

      const def = voices.find(v => v.name === DEFAULT_MS_TTS_VOICE_NAME) || voices[0];
      state.setSelectedMsTTSVoiceName(def.name);
      dom.msTtsVoiceSelect.value = def.name;
    }

    ui.updateStatus('Microsoft TTS voices loaded.');
    return voices;
  } catch (err) {
    console.error('Error fetching MS voices:', err);
    dom.msTtsVoiceSelect.innerHTML = '<option value="">Error loading MS voices</option>';
    state.setFetchedMsTTSVoices([]);
    ui.updateStatus(`Error loading MS voices: ${err.message}`);
    return [];
  } finally {
    state.setAreMsVoicesLoading(false);
    ui.updateAudioControlsUI();
  }
}

/** 2️⃣ Low-level Azure synth */
async function synthesizeWithMicrosoftTTS(ssml, voiceName, langCode) {
  if (!MS_TTS_API_KEY || !MS_TTS_SERVICE_REGION) {
    throw new Error('Microsoft TTS API key or region missing.');
  }

  const endpoint = `https://${MS_TTS_SERVICE_REGION}.tts.speech.microsoft.com/cognitiveservices/v1`;

  const wrappedSSML = /^<speak\b/i.test(ssml.trim())
    ? ssml
    : `<speak version='1.0' xml:lang='${langCode}'>
         <voice name='${voiceName}'>
           <prosody rate='${MS_TTS_SPEECH_SPEED}'>${ssmlEscapeXmlEntities(ssml)}</prosody>
         </voice>
       </speak>`;

  const res = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Ocp-Apim-Subscription-Key': MS_TTS_API_KEY,
      'Content-Type': 'application/ssml+xml',
      'X-Microsoft-OutputFormat': MS_TTS_SAMPLE_RATE_HERTZ,
    },
    body: wrappedSSML,
  });

  if (!res.ok) {
    const txt = await res.text();
    throw new Error(`Azure TTS failed (${res.status}): ${txt || res.statusText}`);
  }

  const buf = await res.arrayBuffer();
  return { audioBlob: new Blob([buf], { type: 'audio/mpeg' }), mimeType: 'audio/mpeg' };
}

/** 3️⃣ Re-process current doc with Azure TTS (UI button) */
export async function handleReprocessWithMicrosoftTTS() {
  const reason = ui.getMsTTSDisabledReason();
  if (reason) {
    ui.updateStatus(`Cannot reprocess with Microsoft TTS: ${reason}`);
    return;
  }

  const voice = state.fetchedMsTTSVoices.find(v => v.name === state.selectedMsTTSVoiceName);
  if (!voice) {
    ui.updateStatus('Selected Microsoft TTS voice is invalid.');
    return;
  }

  state.setIsMsTTSReprocessing(true);
  ui.updateStatus(`Reprocessing "${state.currentDocumentFile.name}" with Microsoft TTS (${voice.label})…`);
  ui.updateAudioControlsUI();

  try {
    let text = '';
    if (state.isDocEditing) {
      text = dom.docEditorTextarea.value;
    } else {
      // Handle both txt (pre element) and docx (HTML content) files
      if (state.currentDocumentFile.type === 'txt') {
        text = dom.docHtmlViewer.querySelector('pre')?.textContent || '';
      } else if (state.currentDocumentFile.type === 'docx') {
        // For DOCX files, extract text from HTML content
        text = dom.docHtmlViewer.textContent || dom.docHtmlViewer.innerText || '';
      }
    }

    const { audioBlob, mimeType } = await synthesizeWithMicrosoftTTS(text, voice.name, voice.languageCode);

    const ext  = state.currentAudioFile.name.slice(state.currentAudioFile.name.lastIndexOf('.')).toLowerCase();
    const base = state.currentAudioFile.name.replace(/^\((G-TTS|MS-TTS)\)\s*/, '').replace(ext, '');
    const fileName = `(MS-TTS) ${base}${ext}`;
    const url = URL.createObjectURL(audioBlob);

    const entry = {
      id: `${fileName}_${Date.now()}`,
      file: new File([audioBlob], fileName, { type: mimeType }),
      name: fileName,
      duration: 0,
      objectURL: url,
      isSynthesized: true,
      ttsProvider: 'microsoft',
    };

    const tmp = new Audio(url);
    tmp.onloadedmetadata = () => {
      entry.duration = tmp.duration;
      updateMusicFileListWithNewTTS(entry, 'Microsoft TTS');
    };
    tmp.onerror = () => updateMusicFileListWithNewTTS(entry, 'Microsoft TTS');
  } catch (err) {
    console.error('Microsoft TTS error:', err);
    ui.updateStatus(`Error during Microsoft TTS reprocessing: ${err.message}`);
  } finally {
    state.setIsMsTTSReprocessing(false);
    ui.updateAudioControlsUI();
  }
}

/*===================================================================
  Verification helper (Google + Microsoft)
===================================================================*/
export async function synthesizeTextToAudio(
  text,
  voiceName,
  provider,
  originalBase,
  originalExt,
) {
  let audioData, label, lang;

  if (provider === 'google') {
    const v = state.fetchedGTTSVoices.find(vo => vo.name === voiceName);
    if (!v) throw new Error(`Google voice ${voiceName} not found.`);
    lang = v.languageCode;

    // Build SSML for Google Cloud TTS
    const ssml = /^<speak\b/i.test(text.trim())
      ? text
      : `<speak><prosody rate="${G_TTS_SPEECH_SPEED}">${ssmlEscapeXmlEntities(text)}</prosody></speak>`;

    const enc = EXTENSION_TO_ENCODING_GOOGLE[originalExt] || 'MP3';
    audioData = await synthesizeWithGoogleTTS(ssml, voiceName, lang, enc);
    label = 'Google Cloud TTS';
  } else if (provider === 'microsoft') {
    const v = state.fetchedMsTTSVoices.find(vo => vo.name === voiceName);
    if (!v) throw new Error(`Microsoft voice ${voiceName} not found.`);
    lang = v.languageCode;
    audioData = await synthesizeWithMicrosoftTTS(text, voiceName, lang);
    label = 'Microsoft TTS';
  } else {
    throw new Error(`Unsupported provider: ${provider}`);
  }

  const { audioBlob, mimeType } = audioData;
  const url  = URL.createObjectURL(audioBlob);
  const file = `(${provider.toUpperCase()}-Verified) ${originalBase}${originalExt}`;

  return new Promise(resolve => {
    const entry = {
      id: `${file}_${Date.now()}`,
      file: new File([audioBlob], file, { type: mimeType }),
      name: file,
      duration: 0,
      objectURL: url,
      isSynthesized: true,
      ttsProvider: provider,
      voiceName,
    };

    const tmp = new Audio(url);
    tmp.onloadedmetadata = () => {
      entry.duration = tmp.duration;
      console.log(`${label} synthesized verification clip: ${entry.name} (${entry.duration}s)`);
      resolve(entry);
    };
    tmp.onerror = () => resolve(entry);
  });
}
